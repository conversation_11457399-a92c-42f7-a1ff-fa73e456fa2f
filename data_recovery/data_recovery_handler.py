import json
import os
import time

import pandas as pd
from PySide6.QtCore import Q<PERSON><PERSON>, Slot, QDate, QTimeZone, QTime, Qt, QThread, Signal, QMutex, QMetaObject, QTimer
from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import QPlainTextEdit, QTextEdit, QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit, \
    QLabel


class DataCollector(QObject):
    """Collects data from UI without blocking the main thread"""
    data_collected = Signal(dict)  # Signal emitted when data is collected

    def __init__(self, auto_saver):
        super().__init__()
        self.auto_saver = auto_saver
        self.mutex = QMutex()  # For thread safety

    @Slot()
    def collect_data(self):
        """Collect data from UI (runs in worker thread)"""
        try:
            # Lock the mutex to ensure thread safety
            self.mutex.lock()

            try:
                # Read existing data
                try:
                    with open(self.auto_saver.json_path, 'r') as f:
                        data = json.load(f)
                except (FileNotFoundError, json.JSONDecodeError):
                    data = {}

                # Get all form data
                form_data = self.auto_saver.get_all_test_data()

                # Update data with form data
                for group_name, group_data in form_data.items():
                    if group_name not in data:
                        data[group_name] = {}
                    data[group_name].update(group_data)

                # Add additional data (temperature, pressure, photos, plots, etc.)
                self.collect_additional_data(data)

                # Emit the collected data
                self.data_collected.emit(data)

            finally:
                # Always unlock the mutex
                self.mutex.unlock()

        except Exception as e:
            # print(f"Error collecting data: {str(e)}")
            import traceback
            traceback.print_exc()

    def collect_additional_data(self, data):
        """Collect additional data from mainwindow"""
        try:
            # print("\n==== COLLECTING ADDITIONAL DATA ====\n")
            mainwindow = self.auto_saver.mainwindow

            # Debug: Print available attributes
            # print("Available mainwindow attributes:")
            for attr in dir(mainwindow):
                if not attr.startswith('_'):
                    # print(f"  - {attr}")
                    pass

            # Add temperature data if available
            # print("\nChecking for temperature data...")
            if hasattr(mainwindow, 'temperature_data'):
                # print(f"  - temperature_data attribute exists: {type(mainwindow.temperature_data)}")
                if mainwindow.temperature_data is not None:
                    # print(f"  - temperature_data is not None")
                    try:
                        temp_df = mainwindow.temperature_data
                        # print(f"  - Got temperature dataframe with shape: {temp_df.shape}")
                        # print(f"  - Columns: {temp_df.columns.tolist()}")
                    except Exception as e:
                        # print(f"Error collecting temperature data: {str(e)}")
                        import traceback
                        traceback.print_exc()
                else:
                    # print("  - temperature_data is None")
                    pass
            else:
                # print("  - No temperature_data attribute found")
                pass

            # Try a different approach to access temperature data
            # print("\nTrying direct access to temperature data...")
            try:
                # Check if we can access the temperature data through UI elements
                if hasattr(mainwindow, 'ui'):
                    # print("  - UI attribute found")
                    # Try to find temperature data in various places
                    if hasattr(mainwindow, 'temperature_data') and mainwindow.temperature_data is not None:
                        temp_df = mainwindow.temperature_data
                        # print(f"  - Using mainwindow.temperature_data")
                    else:
                        # print("  - No valid temperature data found")
                        # Skip creating fallback data
                        return  # Exit the method to prevent creating fallback data

                    # Check if 'time' column exists, if not use the first column or create an index
                    if 'time' in temp_df.columns:
                        time_col = 'time'
                        time_data = temp_df['time'].tolist()
                        # print(f"  - Using 'time' column with {len(time_data)} entries")
                        # Add firing duration if time column exists
                        if len(time_data) > 0:
                            firing_duration = time_data[-1]
                            data['firing_duration'] = firing_duration
                            # print(f"  - Set firing_duration to {firing_duration}")
                    elif 'Time' in temp_df.columns:
                        time_col = 'Time'
                        time_data = temp_df['Time'].tolist()
                        # print(f"  - Using 'Time' column with {len(time_data)} entries")
                        # Add firing duration if Time column exists
                        if len(time_data) > 0:
                            firing_duration = time_data[-1]
                            data['firing_duration'] = firing_duration
                            # print(f"  - Set firing_duration to {firing_duration}")
                    else:
                        # If no time column exists, use the index as time
                        time_col = None
                        time_data = temp_df.index.tolist()
                        # print(f"  - Using index as time with {len(time_data)} entries")

                    # Create the temperature data dictionary
                    temp_data_dict = {'time': time_data}

                    # Add all columns except the time column
                    temp_data_dict['temperatures'] = {
                        col: temp_df[col].tolist()
                        for col in temp_df.columns if col != time_col
                    }

                    data['temperature_data'] = temp_data_dict
                    # print(f"  - Added temperature data with {len(temp_data_dict['temperatures'])} columns")
                    # print(f"  - Temperature columns: {list(temp_data_dict['temperatures'].keys())}")
                else:
                    # print("  - No UI attribute found")
                    pass
            except Exception as e:
                # print(f"Error collecting temperature data: {str(e)}")
                import traceback
                traceback.print_exc()

            # Add pressure data if available
            # print("\nChecking for pressure data...")
            if hasattr(mainwindow, 'pressure_data'):
                # print(f"  - pressure_data attribute exists: {type(mainwindow.pressure_data)}")
                if mainwindow.pressure_data is not None:
                    # print(f"  - pressure_data is not None")
                    try:
                        pressure_df = mainwindow.pressure_data
                        # print(f"  - Got pressure dataframe with shape: {pressure_df.shape}")
                        # print(f"  - Columns: {pressure_df.columns.tolist()}")

                        # Check if 'time' column exists, if not use the first column or create an index
                        if 'time' in pressure_df.columns:
                            time_col = 'time'
                            time_data = pressure_df['time'].tolist()
                            # print(f"  - Using 'time' column with {len(time_data)} entries")
                        elif 'Time' in pressure_df.columns:
                            time_col = 'Time'
                            time_data = pressure_df['Time'].tolist()
                            # print(f"  - Using 'Time' column with {len(time_data)} entries")
                        else:
                            # If no time column exists, use the index as time
                            time_col = None
                            time_data = pressure_df.index.tolist()
                            # print(f"  - Using index as time with {len(time_data)} entries")

                        # Create the pressure data dictionary
                        pressure_data_dict = {'time': time_data}

                        # Add all columns except the time column
                        pressure_data_dict['pressures'] = {
                            col: pressure_df[col].tolist()
                            for col in pressure_df.columns if col != time_col
                        }

                        data['pressure_data'] = pressure_data_dict
                        # print(f"  - Added pressure data with {len(pressure_data_dict['pressures'])} columns")
                        # print(f"  - Pressure columns: {list(pressure_data_dict['pressures'].keys())}")
                    except Exception as e:
                        # print(f"Error collecting pressure data: {str(e)}")
                        import traceback
                        traceback.print_exc()
                else:
                    # print("  - pressure_data is None")
                    pass
            else:
                # print("  - No pressure_data attribute found")
                pass

            # Skip creating fallback pressure data
            if 'pressure_data' not in data:
                # print("  - No pressure data found, skipping fallback creation")
                pass
            # Add temperature analysis if available
            if hasattr(mainwindow, 'temp_analyzer') and \
               hasattr(mainwindow.temp_analyzer, 'analysis_results'):
                try:
                    if 'matrix' in mainwindow.temp_analyzer.analysis_results:
                        analysis_df = mainwindow.temp_analyzer.analysis_results['matrix']
                        if isinstance(analysis_df, pd.DataFrame):
                            data['temperature_analysis'] = analysis_df.to_dict(orient='index')
                            # print("Collected temperature analysis data")
                        else:
                            pass
                            # print(f"Temperature analysis matrix is not a DataFrame: {type(analysis_df)}")
                except Exception as e:
                    # print(f"Error collecting temperature analysis: {str(e)}")
                    pass

            # Add filtered temperature data if available
            if hasattr(mainwindow, 'filtered_temp_data') and mainwindow.filtered_temp_data is not None:
                try:
                    filtered_df = mainwindow.filtered_temp_data

                    # Check if 'time' column exists, if not use the first column or create an index
                    if 'time' in filtered_df.columns:
                        time_col = 'time'
                        time_data = filtered_df['time'].tolist()
                    elif 'Time' in filtered_df.columns:
                        time_col = 'Time'
                        time_data = filtered_df['Time'].tolist()
                    else:
                        # If no time column exists, use the index as time
                        time_col = None
                        time_data = filtered_df.index.tolist()

                    # Create the filtered temperature data dictionary
                    filtered_data_dict = {'time': time_data}

                    # Add all columns except the time column
                    filtered_data_dict['temperatures'] = {
                        col: filtered_df[col].tolist()
                        for col in filtered_df.columns if col != time_col
                    }

                    data['filtered_temp_data'] = filtered_data_dict
                    # print("Collected filtered temperature data")
                except Exception as e:
                    # print(f"Error collecting filtered temperature data: {str(e)}")
                    pass

                # Add plot information if available using the new utility function
                try:
                    # Import the plot_utils module
                    from data_recovery.plot_utils import save_plots_for_autosave

                    # Use the utility function to save plots
                    data = save_plots_for_autosave(mainwindow, data)

                except Exception as e:
                    # print(f"Error collecting plot data: {str(e)}")
                    import traceback
                    traceback.print_exc()

            # Add photo information if available
            # print("\nChecking for photo information...")
            if hasattr(mainwindow, 'photo_widgets'):
                # print(f"  - photo_widgets attribute exists")
                try:
                    photos_metadata = {}
                    # print(f"  - Photo widget types: {list(mainwindow.photo_widgets.keys())}")

                    for photo_type, widget_info in mainwindow.photo_widgets.items():
                        try:
                            # print(f"  - Processing photo type: {photo_type}")
                            # print(f"  - Widget info keys: {list(widget_info.keys())}")

                            if 'line_edit' in widget_info and hasattr(widget_info['line_edit'], 'text'):
                                photo_path = widget_info['line_edit'].text()
                                # print(f"  - Photo path: {photo_path}")

                                if photo_path and os.path.exists(photo_path) and photo_path != 'No Photo Selected':
                                    photos_metadata[photo_type] = {
                                        'path': photo_path,
                                        'label': widget_info.get('label', f'Photo {photo_type}')
                                    }
                                    # print(f"  - Added photo: {photo_type} -> {photo_path}")
                                else:
                                    if not photo_path:
                                        # print(f"  - Photo path is empty")
                                        pass
                                    elif not os.path.exists(photo_path):
                                        # print(f"  - Photo path does not exist: {photo_path}")
                                        pass
                                    elif photo_path == 'No Photo Selected':
                                        # print(f"  - 'No Photo Selected' text found")
                                        pass
                            else:
                                # print(f"  - No line_edit in widget_info or no text method")
                                pass
                        except Exception as e:
                            # print(f"Error processing photo {photo_type}: {str(e)}")
                            import traceback
                            traceback.print_exc()

                    if photos_metadata:
                        data['photos'] = photos_metadata
                        # print(f"  - Added {len(photos_metadata)} photos to data")
                    else:
                        # print("  - No valid photos found")
                        pass
                except Exception as e:
                    # print(f"Error collecting photo information: {str(e)}")
                    import traceback
                    traceback.print_exc()
            else:
                print("  - No photo_widgets attribute found")

        except Exception as e:
            print(f"Error collecting additional data: {str(e)}")
            import traceback
            traceback.print_exc()


class SaveWorker(QObject):
    """Worker thread for saving data to avoid blocking the UI"""
    finished = Signal()  # Signal emitted when save operation is complete
    progress = Signal(str)  # Signal to report progress

    def __init__(self):
        super().__init__()
        self.json_path = None
        self.data = None
        self.mutex = QMutex()  # For thread safety
        self.last_save_time = 0  # Track when the last save occurred

    @Slot(str, dict)
    def prepare_save(self, json_path, data):
        """Prepare data for saving"""
        self.json_path = json_path
        self.data = data

    @Slot()
    def save(self):
        """Save data to JSON file (runs in worker thread)"""
        try:
            self.progress.emit("\n==== SAVE OPERATION STARTED ====\n")

            # Throttle saves to at most once per second
            current_time = time.time()
            if current_time - self.last_save_time < 1.0:
                self.progress.emit(f"Throttling save operation for {1.0 - (current_time - self.last_save_time):.2f} seconds")
                time.sleep(1.0 - (current_time - self.last_save_time))

            self.last_save_time = time.time()

            # Check if we have data to save
            if self.json_path is None or self.data is None:
                self.progress.emit("No data to save - path or data is None")
                return

            self.progress.emit(f"Saving data to {self.json_path}")
            if self.data:
                self.progress.emit(f"Data has {len(self.data)} top-level keys: {list(self.data.keys())}")

                # Check for specific data types
                if 'temperature_data' in self.data:
                    self.progress.emit(f"Temperature data found with {len(self.data['temperature_data']['temperatures'])} columns")
                else:
                    self.progress.emit("No temperature data found")

                if 'pressure_data' in self.data:
                    self.progress.emit(f"Pressure data found with {len(self.data['pressure_data']['pressures'])} columns")
                else:
                    self.progress.emit("No pressure data found")

                if 'photos' in self.data:
                    self.progress.emit(f"Photo data found with {len(self.data['photos'])} photos")
                else:
                    self.progress.emit("No photo data found")
            else:
                self.progress.emit("Data is empty")

            # Lock the mutex to ensure thread safety
            self.mutex.lock()

            try:
                # Try to save directly to the file
                self.progress.emit(f"Saving directly to file: {self.json_path}")

                try:
                    # Write data directly to the file
                    with open(self.json_path, 'w') as f:
                        json.dump(self.data, f)
                        self.progress.emit(f"Data written directly to file")
                except PermissionError:
                    # If we get a permission error, try a different approach
                    self.progress.emit(f"Permission error, trying alternative approach...")

                    # Create a new file with a unique name
                    import uuid
                    unique_path = f"{os.path.dirname(self.json_path)}\\temp_data_{uuid.uuid4().hex}.json"
                    self.progress.emit(f"Creating file with unique name: {unique_path}")

                    # Write data to the unique file
                    with open(unique_path, 'w') as f:
                        json.dump(self.data, f)
                        self.progress.emit(f"Data written to unique file")

                    # Try to copy the unique file to the final file
                    try:
                        import shutil
                        shutil.copy2(unique_path, self.json_path)
                        self.progress.emit(f"Unique file copied to final file")
                        os.remove(unique_path)  # Clean up the unique file
                    except Exception as e:
                        self.progress.emit(f"Error copying unique file: {str(e)}")
                        self.progress.emit(f"Data saved to: {unique_path}")

                # Verify the file was saved correctly
                if os.path.exists(self.json_path):
                    file_size = os.path.getsize(self.json_path)
                    self.progress.emit(f"File saved successfully: {self.json_path} ({file_size} bytes)")

                    # Read the file back to verify its contents
                    try:
                        with open(self.json_path, 'r') as f:
                            saved_data = json.load(f)
                            self.progress.emit(f"Verified file contains {len(saved_data)} top-level keys")
                    except Exception as e:
                        self.progress.emit(f"Error verifying saved file: {str(e)}")
                else:
                    self.progress.emit(f"Warning: File does not exist after save: {self.json_path}")

                self.progress.emit("Save operation completed successfully")
            finally:
                # Always unlock the mutex
                self.mutex.unlock()

        except Exception as e:
            self.progress.emit(f"Error during save operation: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            # Always emit the finished signal
            self.finished.emit()


class AutoSaver(QObject):
    # Signal to trigger data collection
    collect_requested = Signal()

    def __init__(self, mainwindow):
        # Initialize parent class first
        super().__init__()

        # Store a reference to the timer so we can stop it later
        self.auto_save_timer = None

        # Store references to mainwindow and UI
        self.ui = mainwindow.ui
        self.mainwindow = mainwindow

        # Thread management
        self.collector_thread = None
        self.collector = None
        self.save_thread = None
        self.save_worker = None
        self.save_in_progress = False
        self.mutex = QMutex()  # For thread safety

        # Use a path in the user's home directory for temp_data.json to avoid permission issues
        import os
        user_home = os.path.expanduser("~")
        self.json_path = os.path.join(user_home, "temp_data.json")
        print(f"Using JSON path: {self.json_path}")

        # Initialize empty JSON file if it doesn't exist
        try:
            with open(self.json_path, 'r') as f:
                json.load(f)
                print(f"Successfully loaded existing temp_data.json")
        except (FileNotFoundError, json.JSONDecodeError):
            print(f"Creating new temp_data.json file")
            with open(self.json_path, 'w') as f:
                json.dump({}, f)

        # Set up the worker threads
        self.setup_worker_threads()

        # Set up auto-save timer (save every 10 minutes
        self.auto_save_timer = QTimer(self)
        self.auto_save_timer.timeout.connect(self.trigger_auto_save)
        self.auto_save_timer.start(600000)  # 10 minutes (600,000 ms)

    def setup_worker_threads(self):
        """Set up the worker threads for data collection and saving"""
        try:
            # Create data collector thread
            self.collector_thread = QThread()
            self.collector = DataCollector(self)
            self.collector.moveToThread(self.collector_thread)

            # Connect signals for data collector
            self.collect_requested.connect(self.collector.collect_data)
            self.collector.data_collected.connect(self.on_data_collected)

            # Start the collector thread
            self.collector_thread.start()

            # Create save worker thread
            self.save_thread = QThread()
            self.save_worker = SaveWorker()
            self.save_worker.moveToThread(self.save_thread)

            # Connect signals for save worker
            # self.save_worker.progress.connect(self.log_progress)
            self.save_worker.finished.connect(self.on_save_finished)

            # Start the save thread
            self.save_thread.start()

            print("Worker threads initialized successfully")

        except Exception as e:
            print(f"Error setting up worker threads: {str(e)}")
            import traceback
            traceback.print_exc()

        # Defining parameter groups
        self.user_input_parameters = {
            'basic_info': {
                'test_no': (self.ui.subLnEdtTestNo, 'value'),
                'aim': (self.ui.subLnEdtAim, 'text'),
                'propellant': (self.ui.subLnEdtProp, 'text'),
                'propellant_ri': (self.ui.subLnEdtPropRI, 'value'),
                'catalyst': (self.ui.subLnEdtCat, 'text'),
                'test_date': (self.ui.subLnEdtTestDate, 'date'),
            },

            'system_specs': {
                'chamber_no': (self.ui.subLnEdtChmbrNo, 'text'),
                'chamber_material': (self.ui.subLnEdtChmbrMat, 'text'),
                'chamber_depth': (self.ui.subLnEdtChmbrDept, 'value'),
                'internal_chamb_dia': (self.ui.subLnEdtInternalChmbrDia, 'value'),
                'external_chamb_dia': (self.ui.subLnEdtExternalChmbrDia, 'value'),
                'nozzle_throat_dia': (self.ui.subLnEdtNozlThrtDime, 'value'),
                'ret_plate_ori_dia': (self.ui.subLnEdtRetainerPltOrfcDia, 'value'),
                'mesh_mat': (self.ui.subLnEdtMeshMat, 'text'),
                'mesh_size': (self.ui.subLnEdtMeshSize, 'text'),
            },

            'propellant_specs': {
                'propellant_type': (self.ui.subLnEdtTypeOfProp, 'text'),
                'prop_conc_bef': (self.ui.subLnEdtConcBefTest, 'value'),
                'prop_stability': (self.ui.subLnEdtStability, 'text'),
                'prop_weight_bef': (self.ui.subLnEdtWghtOfPropBefTest, 'value'),
                'prop_weight_aft': (self.ui.subLnEdtWghtOfPropAftTest, 'value'),
            },

            'catalyst_specs': {
                'catalyst_type': (self.ui.subLnEdtCatType, 'text'),
                'catalyst_grade': (self.ui.subLnEdtCatGrade, 'text'),
                'catalyst_size': (self.ui.subLnEdtCatSize, 'text'),
                'cat_weight_bef': (self.ui.subLnEdtCatWghtBefTest, 'value'),
                # 'preheat_temp': (ui.subLnEdtPrehtTemp, 'text'),
            },

            'component_details':{
                'Pressure_sensor_type_vac_chamb': (self.ui.Vac_Chamb_Pressure_Sensr_type_Input, 'text'),
                'Pressure_sensor_number_&_slope_equation_vac_chamb': (self.ui.Vac_Chamb_Pressure_Snsr_No_Slope_Eqn_Input, 'text'),
                'Pressure_sensor_range_vac_chamb': (self.ui.Vac_Chamb_Pressure_Snsr_range_Input, 'text'),
                'Pressure_sensor_input_and_output_vac_chamb': (self.ui.Vac_Chamb_Pressure_Snsr_IO_Input, 'text'),
                'Pressure_sensor_type_prop_tank': (self.ui.Prop_Tank_Pressure_Sensr_type_Input, 'text'),
                'Pressure_sensor_number_&_slope_equation_prop_tank': (self.ui.Prop_Tank__Pressure_Snsr_No_Slope_Eqn_Input, 'text'),
                'Pressure_sensor_range_prop_tank': (self.ui.Prop_Tank_Pressure_Snsr_range_Input, 'text'),
                'Pressure_sensor_input_and_output_prop_tank': (self.ui.Prop_Tank__Pressure_Snsr_IO_Input, 'text'),
                'Pressure_sensor_type_thruster': (self.ui.Thruster_Pressure_Sensr_type_Input, 'text'),
                'Pressure_sensor_number_&_slope_equation_thruster': (self.ui.Thruster_Pressure_Snsr_No_Slope_Eqn_Input, 'text'),
                'Pressure_sensor_range_thruster': (self.ui.Thruster_Pressure_Snsr_range_Input, 'text'),
                'Pressure_sensor_input_and_output_thruster': (self.ui.Thruster_Pressure_Snsr_IO_Input, 'text'),
                'Heater_type': (self.ui.subLnEdtHtrType, 'text'),
                'Heater_input_power (W)': (self.ui.subLnEdtHtrInpPower, 'text')
            },

            'test_details': {
                'prop_tank_htr_cut_off_temp': (self.ui.subLnEdtPropTnkHtrCtOfTemp, 'value'),
                'prop_tank_htr_rst_temp': (self.ui.subLnEdtPropTnkHtrRstTemp, 'value'),
                'test_procedure': (self.ui.subLblTestProcValue, 'plainText'),
            },

            'heater_info':{
                'heater_type': (self.ui.subLnEdtHtrType, 'text'),
                'heater_input_Voltage': (self.ui.subLnEdtHtrInpVoltage, 'value'),
                'heater_input_Current': (self.ui.subLnEdtHtrInpCurrent, 'value'),
                'heater_input_Wattage': (self.ui.subLnEdtHtrInpWattage, 'value'),
                'heater_cut_off_temp': (self.ui.subLnEdtHtrCtOfTemp, 'value'),
                'heater_rst_temp': (self.ui.subLnEdtHtrRstTemp, 'value')
            },

            'heater_cycles': {
                **{f'heater_switch_on_time_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOnTime'), 'time') for i in
                   range(1, 5)},
                **{f'heater_switch_on_corresponding_tank_pressure_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtONCorspgTankPressure'), 'value') for i in
                   range(1, 5)},
                **{f'heater_switch_on_corresponding_thruster_pressure_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtONCorspgThrusterPressure'), 'value') for i in
                   range(1, 5)},
                **{f'heater_switch_off_time_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOffTime'), 'time') for i
                   in range(1, 5)},
                **{f'heater_switch_off_corresponding_tank_pressure_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOFFCorspgTankPressure'), 'value') for i in
                   range(1, 5)},
                **{f'heater_switch_off_corresponding_thruster_pressure_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtHtrSwtOFFCorspgThrusterPressure'), 'value') for i in
                   range(1, 5)},
                **{f'max_temp_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtMaxTemp'), 'value') for i in range(1, 5)},
                **{f'location_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtLoc'), 'text') for i in range(1, 5)},
                **{f'corres_temp_cyc_{i}': (getattr(self.ui, f'cyc{i}SubLnEdtCorrespgTemp'), 'value') for i in
                   range(1, 5)},
            },

            'post_test_obsvs':{
                'chamber_No': (self.ui.subLnEdtChmbrNoPostTestObs, 'text'),
                'chamber_length': (self.ui.subLnEdtChmbrLen_2, 'value'),
                'chamber_internal_dia': (self.ui.subLnEdtChmbrIntDia_2, 'value'),
                'chamber_external_dia': (self.ui.subLnEdtChmbrExtDia_2, 'value'),
                'mesh_condition': (self.ui.subLnEdtMeshCond_2, 'text'),
                'retainer_plate_condition': (self.ui.subLnEdtRetainerPltCond_2, 'text'),
                'observations': (self.ui.subLnEdtNote, 'plainText')
            },

            'catalyst_post_analysis':{
                'catalyst_detail': (self.ui.subLnEdtCatDet, 'text'),
                'catalyst_col_bef': (self.ui.subLnEdtCatColBfr, 'text'),
                'catalyst_colo_aft': (self.ui.subLnEdtCatColAft, 'text'),
                'catalyst_weight_filled': (self.ui.subLnEdtCatWghtFild, 'value'),
                'catalyst_weight_recovrd': (self.ui.subLnEdtCatWghtRecvrd, 'value'),
                'catalyst_loss_perctg': (self.ui.subLnEdtCatLosPerc, 'value')
            },

            'propellant_post_analysis':{
                'prop_detail': (self.ui.subLnEdtPropDet_2, 'text'),
                'prop_col_bef': (self.ui.subLnEdtPropColBef_2, 'text'),
                'prop_col_aft': (self.ui.subLnEdtPropColAft_2, 'text'),
                'prop_wght_filled': (self.ui.subLnEdtPropWghtFild_2, 'value'),
                'prop_wght_recovrd': (self.ui.subLnEdtPropWghtRecvrd_2, 'value'),
                'prop_used_perctg': (self.ui.subLnEdtPropUsedPerc_2, 'value'),
                'prop_ri_bef_firg': (self.ui.subLnEdtPropRIBefFirg_2, 'value'),
                'prop_ri_aft_firg': (self.ui.subLnEdtPropRIAftFirg_2, 'value'),
                'firg_durtn': (self.ui.subLnEdtFirgDur_2, 'value'),
                'aprox_mass_flow_rate': (self.ui.subLnEdtApproxMassFlowRate_2, 'value'),\
                'prop_conc_bef_table': (self.ui.subLblPropBefConcTable, 'text'),
                'prop_conc_aft_table': (self.ui.subLblPropAftConcTable, 'text'),
                'prop_ri_bef_table': (self.ui.subLblPropBefRITable, 'text'),
                'prop_ri_aft_table': (self.ui.subLblPropAftRITable, 'text'),
            },

            'test_authorization':{
                'test_conducted_by': (self.ui.subLnEdtTestConductedBy, 'text'),
                'report_generated_by': (self.ui.subLnEdtReportGeneratedBy, 'text'),
                'report_authorized_by': (self.ui.subLnEdtReportAuthorizedBy, 'text')
            },

            'pressure_relations':{
                'Vacuum_chamber_pressure_relation': (self.ui.lnEdtY0PressureRelation, 'text'),
                'Propellant_tank_pressure_relation': (self.ui.lnEdtY1PressureRelation, 'text'),
                'Thruster_chamber_pressure_relation': (self.ui.lnEdtY2PressureRelation, 'text')
            },

            'performance_data':{
                'chamber_pressure': (self.ui.subLnEdtChambPressure, 'text'),
                'vacuum_chamber_pressure': (self.ui.subLnEdtVacPressure, 'text'),
                'maximum_temperature': (self.ui.subLnEdtChambTemp, 'text'),
                'characteristic_velocity': (self.ui.subLnEdtCharVelo, 'text'),
                'coefficient_of_thrust': (self.ui.subLnEdtCoefOfThrust, 'text'),
                'Burn_time (s)': (self.ui.subLnEdtBurnTime, 'text'),
                'mass_flow_rate': (self.ui.subLnEdtMassFlowRate, 'text'),
                'thrust': (self.ui.subLnEdtThrust, 'text'),
                'specific_impulse': (self.ui.subLnEdtSpcImpulse, 'text'),
                'total_impulse': (self.ui.subLnEdtTotImpulse, 'text'),
                'Weight_of_propellant_before_the_test_(g)_performance': (self.ui.lnEdtInitialPropMass, 'value'),
                'Weight_of_propellant_after_the_test_(g)_performance': (self.ui.lnEdtFinalPropMass, 'value'),
                'vacuum_pressure_lower_limit': (self.ui.lnEdtVacPressRangeMin, 'value'),
                'vacuum_pressure_upper_limit': (self.ui.lnEdtVacPressRangeMax, 'value'),
                'chamber_pressure_lower_limit': (self.ui.lnEdtChambPressRangeMin, 'value'),
                'chamber_pressure_upper_limit': (self.ui.lnEdtChambPressRangeMax, 'value')
            }
        }

        # First connect all inputs, then load data
        self.connect_all_inputs()

        # Now load the data from the JSON file
        print("Loading data from temp_data.json...")
        self.load_data_from_json(self.json_path)

        # Trigger an initial auto-save to ensure all data is saved
        # This will run after a short delay to allow the UI to initialize
        QTimer.singleShot(5000, self.force_save_all_data)
        QTimer.singleShot(10000, self.trigger_auto_save)

    def get_widget_value(self, widget, value_type):
        """Helper method to get widget value based on type"""
        try:
            if widget is None:
                return None

            if value_type == 'value':
                if hasattr(widget, 'value'):
                    return float(widget.value())
                elif hasattr(widget, 'text'):
                    try:
                        return float(widget.text())
                    except ValueError:
                        return 0.0
            elif value_type == 'text':
                if hasattr(widget, 'text'):
                    return str(widget.text())
                elif isinstance(widget, QLabel):
                    return str(widget.text())
            elif value_type == 'plainText':
                return str(widget.toPlainText())
            elif value_type == 'date':
                return widget.date().toString('dd/MM/yyyy')
            elif value_type == 'time':
                return widget.time().toString('HH:mm')
            return None
        except (ValueError, AttributeError) as e:
            print(f"Error getting widget value: {str(e)}")
            return None

    def set_widget_value(self, widget, value_type, value):
        """Helper method to set widget value based on type"""
        try:
            if not value:
                return

            if value_type == 'value':
                widget.setValue(float(value))
            elif value_type == 'text':
                widget.setText(str(value))
            elif value_type == 'plainText':
                widget.setPlainText(str(value))
            elif value_type == 'date':
                widget.setDate(QDate.fromString(str(value), 'dd/MM/yyyy'))
            elif value_type == 'time':
                widget.setTime(QTime.fromString(str(value), 'HH:mm'))
        except (ValueError, AttributeError) as e:
            print(f"Error setting widget value: {str(e)}")

    def connect_widget(self, widget, field_name, value_type):
        """Connect appropriate signal based on widget type"""
        try:
            if widget is None:
                print(f"Warning: Widget for {field_name} is None, skipping connection")
                return

            # Use a single shared timer for all widgets to reduce the number of timers
            if not hasattr(self, 'shared_save_timer'):
                self.shared_save_timer = QTimer(self)
                self.shared_save_timer.setSingleShot(True)  # Single shot timer
                self.shared_save_timer.setInterval(3000)    # 3 second delay
                self.shared_save_timer.timeout.connect(self.trigger_auto_save)

            if isinstance(widget, (QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QTimeEdit)):
                # For input fields, only save when editing is finished
                widget.editingFinished.connect(self.restart_save_timer)
                print(f"Connected editingFinished signal for {field_name}")

            elif isinstance(widget, (QPlainTextEdit, QTextEdit)):
                # For text areas, use the shared timer to debounce changes
                widget.textChanged.connect(self.restart_save_timer)
                print(f"Connected debounced textChanged signal for {field_name}")

            else:
                print(f"Warning: Unsupported widget type for {field_name}: {type(widget).__name__}")

        except Exception as e:
            print(f"Error connecting widget {field_name}: {str(e)}")

    def restart_save_timer(self):
        """Restart the shared save timer"""
        if hasattr(self, 'shared_save_timer'):
            self.shared_save_timer.start()  # Restart the timer

    def connect_all_inputs(self):
        """Connect all input widgets to save handlers"""
        for group_name, parameters in self.user_input_parameters.items():
            for field_name, (widget, value_type) in parameters.items():
                self.connect_widget(widget, field_name, value_type)

    @Slot()
    def save_field(self, field_name, value, json_file_path):
        """Save field value to JSON file"""
        if value is None:
            return

        try:
            # Just log the field update - we'll save everything during auto-save
            for group_name, parameters in self.user_input_parameters.items():
                if field_name in parameters:
                    # print(f"Field updated: {group_name}.{field_name} = {value}")
                    break
            else:
                print(f"Field updated: {field_name} = {value}")

            # Restart the shared save timer to trigger a save after a delay
            self.restart_save_timer()

        except Exception as e:
            print(f"Error handling field update {field_name}: {str(e)}")

    # This method has been replaced by the collect_additional_data method in the DataCollector class

    def check_and_enable_plots_button(self):
        """Check if temperature or pressure data is valid and enable the plots button"""
        print("\n==== CHECKING PLOTS BUTTON ENABLEMENT FROM AUTOSAVER ====\n")

        # Check if temperature data is valid
        temp_valid = hasattr(self.mainwindow, 'temperature_data') and \
                    self.mainwindow.temperature_data is not None and \
                    not self.mainwindow.temperature_data.empty and \
                    len(self.mainwindow.temperature_data) > 1

        if temp_valid:
            print(f"Temperature data is valid: {self.mainwindow.temperature_data.shape}")
            print(f"Temperature data columns: {self.mainwindow.temperature_data.columns.tolist()}")
        else:
            print("Temperature data is not valid")

        # Check if pressure data is valid
        pressure_valid = hasattr(self.mainwindow, 'pressure_data') and \
                        self.mainwindow.pressure_data is not None and \
                        not self.mainwindow.pressure_data.empty and \
                        len(self.mainwindow.pressure_data) > 1

        if pressure_valid:
            print(f"Pressure data is valid: {self.mainwindow.pressure_data.shape}")
            print(f"Pressure data columns: {self.mainwindow.pressure_data.columns.tolist()}")
        else:
            print("Pressure data is not valid")

        # Enable the plots button if either data is valid
        if temp_valid or pressure_valid:
            print("Enabling plots button due to valid data")
            self.mainwindow.ui.btnPlots.setEnabled(True)

            # Check if the button is actually enabled
            print(f"Plots button enabled state: {self.mainwindow.ui.btnPlots.isEnabled()}")
        else:
            print("No valid data found, plots button not enabled")

    def load_data_from_json(self, json_file_path: str):
        """Load saved data from JSON file"""
        try:
            print(f"Attempting to load data from: {json_file_path}")

            # Check if the file exists
            if not os.path.exists(json_file_path):
                print(f"File not found: {json_file_path}")
                # Try to find any temp_data*.json files in the same directory
                dir_path = os.path.dirname(json_file_path)
                import glob
                temp_files = glob.glob(os.path.join(dir_path, "temp_data*.json"))
                if temp_files:
                    # Use the most recent file
                    most_recent = max(temp_files, key=os.path.getmtime)
                    print(f"Using most recent temp file instead: {most_recent}")
                    json_file_path = most_recent

            with open(json_file_path, 'r') as f:
                try:
                    data = json.load(f)
                    print(f"Successfully loaded JSON data with {len(data)} entries")

                    # Check if data is in flat structure or grouped structure
                    if isinstance(data, dict):
                        # First try to load data assuming grouped structure
                        for group_name, group_data in data.items():
                            if group_name in self.user_input_parameters and isinstance(group_data, dict):
                                print(f"Loading grouped data for: {group_name}")
                                for field_name, field_value in group_data.items():
                                    if field_name in self.user_input_parameters[group_name]:
                                        widget, value_type = self.user_input_parameters[group_name][field_name]
                                        self.set_widget_value(widget, value_type, field_value)
                                        print(f"  Set {group_name}.{field_name} = {field_value}")

                        # # Then try to load data assuming flat structure
                        # for field_name, field_value in data.items():
                        #     # Find which group this field belongs to
                        #     for group_name, parameters in self.user_input_parameters.items():
                        #         if field_name in parameters:
                        #             widget, value_type = parameters[field_name]
                        #             self.set_widget_value(widget, value_type, field_value)
                        #             print(f"Set flat field {field_name} = {field_value}")
                        #             break

                    self.mainwindow.ui.lblCatalyst.setVisible(True)
                    # Show and update top bar elements
                    # First ensure the elements are visible
                    self.mainwindow.ui.lblAim.setVisible(True)
                    self.mainwindow.ui.lblPropellant.setVisible(True)

                    self.mainwindow.ui.testNoFrame.setVisible(True)

                    # Force update the text values
                    aim_text = self.mainwindow.ui.subLnEdtAim.text()
                    prop_text = self.mainwindow.ui.subLnEdtProp.text()
                    cat_text = self.mainwindow.ui.subLnEdtCat.text()
                    test_num = str(self.mainwindow.ui.subLnEdtTestNo.value())

                    self.mainwindow.ui.lblAim.setText(aim_text)
                    self.mainwindow.ui.lblPropellant.setText(prop_text)
                    self.mainwindow.ui.lblCatalyst.setText(cat_text)
                    self.mainwindow.ui.lblTestNumber.setText(test_num)

                    # Force a repaint of the widgets
                    self.mainwindow.ui.lblAim.repaint()
                    self.mainwindow.ui.lblPropellant.repaint()
                    self.mainwindow.ui.lblCatalyst.repaint()
                    self.mainwindow.ui.testNoFrame.repaint()

                    # Debug logging
                    # print("Setting top bar elements:")
                    # print(f"Aim: {aim_text}")
                    # print(f"Propellant: {prop_text}")
                    # print(f"Catalyst: {cat_text}")
                    # print(f"Test Number: {test_num}")

                    from PySide6.QtCore import QTimer

                    # Add a small delay and then force update again using QTimer
                    QTimer.singleShot(100, lambda: self._force_update_labels(aim_text, prop_text, cat_text, test_num))

                    # Load additional data into the mainwindow
                    self.load_additional_data(data)

                    # Explicitly check and enable the plots button
                    self.check_and_enable_plots_button()

                    # Also directly force enable the plots button in the mainwindow
                    if hasattr(self.mainwindow, 'force_enable_plots_button'):
                        self.mainwindow.force_enable_plots_button()

                    # Direct approach to enable the button
                    if hasattr(self.mainwindow, 'ui') and hasattr(self.mainwindow.ui, 'btnPlots'):
                        self.mainwindow.ui.btnPlots.setEnabled(True)

                    # Use a timer to force enable the button after a delay
                    if hasattr(self.mainwindow, 'force_enable_plots_button'):
                        from PySide6.QtCore import QTimer
                        QTimer.singleShot(500, self.mainwindow.force_enable_plots_button)

                except json.JSONDecodeError:
                    print("Error decoding JSON file")
                    self.clear_temp_data()
        except FileNotFoundError:
            print(f"File not found: {json_file_path}")
            self.clear_temp_data()
        except Exception as e:
            print(f"Unexpected error loading JSON data: {str(e)}")
            import traceback
            traceback.print_exc()

    def _force_update_labels(self, aim, prop, cat, test_num):
        """Helper method to force update labels after a small delay"""
        # Update labels again
        self.mainwindow.ui.lblAim.setVisible(True)
        self.mainwindow.ui.lblPropellant.setVisible(True)
        self.mainwindow.ui.lblCatalyst.setVisible(True)
        self.mainwindow.ui.testNoFrame.setVisible(True)

        self.mainwindow.ui.lblAim.setText(aim)
        self.mainwindow.ui.lblPropellant.setText(prop)
        self.mainwindow.ui.lblCatalyst.setText(cat)
        self.mainwindow.ui.lblTestNumber.setText(test_num)

        # Force repaint
        self.mainwindow.ui.lblAim.repaint()
        self.mainwindow.ui.lblPropellant.repaint()
        self.mainwindow.ui.lblCatalyst.repaint()
        self.mainwindow.ui.testNoFrame.repaint()

        print("Forced update of labels completed")

    def load_additional_data(self, data):
        """Load additional data from JSON into the mainwindow"""
        try:
            # Flag to track if we should enable the plots button
            should_enable_plots = False
            # Load temperature analysis if present
            if 'temperature_analysis' in data:
                print("Loading temperature analysis data")
                df = pd.DataFrame.from_dict(data['temperature_analysis'], orient='index')
                if hasattr(self.mainwindow, 'temp_analyzer'):
                    self.mainwindow.temp_analyzer.analysis_results = {'matrix': df}
                    print("Temperature analysis loaded successfully")

                    # Display temperature analysis if the mainwindow has the method
                    if hasattr(self.mainwindow, 'display_temperature_analysis'):
                        self.mainwindow.display_temperature_analysis(df)
                        print("Temperature analysis displayed")

            # Load temperature data if available
            if 'temperature_data' in data:
                try:
                    print("Loading temperature data")
                    temp_data = data['temperature_data']

                    # Create DataFrame with time data
                    if 'time' in temp_data:
                        df_data = {'time': temp_data['time']}
                    else:
                        # If no time data, create a sequence
                        if 'temperatures' in temp_data and temp_data['temperatures']:
                            first_temp_col = next(iter(temp_data['temperatures'].values()))
                            df_data = {'time': list(range(len(first_temp_col)))}
                        else:
                            df_data = {'time': [0]}  # Fallback if no temperature data

                    # Add temperature data
                    if 'temperatures' in temp_data:
                        df_data.update(temp_data['temperatures'])

                    if hasattr(self.mainwindow, 'temperature_data'):
                        self.mainwindow.temperature_data = pd.DataFrame(df_data)
                        print("Temperature data loaded successfully")

                        # Update UI indicators if they exist
                        if hasattr(self.mainwindow, 'ui'):
                            self.mainwindow.ui.btnTempDataInd.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                            self.mainwindow.ui.btnTempDataLoad.setStyleSheet(u'background-color: rgb(6, 196, 142);')
                            self.mainwindow.ui.btnTempMatrix.setEnabled(True)
                            self.mainwindow.ui.btnPlots.setEnabled(True)

                            # Update firing duration if available
                            if 'time' in df_data and df_data['time']:
                                try:
                                    burn_time = df_data['time'][-1]
                                    self.mainwindow.ui.lblFiringDuration.setText(f'{burn_time}s')
                                    self.mainwindow.ui.subLnEdtFirgDur_2.setValue(float(burn_time))
                                except (IndexError, ValueError, TypeError) as e:
                                    print(f"Error setting burn time: {str(e)}")
                except Exception as e:
                    print(f"Error loading temperature data: {str(e)}")

            # Load pressure data if available
            if 'pressure_data' in data:
                try:
                    print("Loading pressure data")
                    pressure_data = data['pressure_data']

                    # Create DataFrame with time data
                    if 'time' in pressure_data:
                        df_data = {'time': pressure_data['time']}
                    else:
                        # If no time data, create a sequence
                        df_data = {'time': list(range(len(next(iter(pressure_data['pressures'].values())))))}

                    # Add pressure data
                    if 'pressures' in pressure_data:
                        df_data.update(pressure_data['pressures'])

                    if hasattr(self.mainwindow, 'pressure_data'):
                        self.mainwindow.pressure_data = pd.DataFrame(df_data)
                        print("Pressure data loaded successfully")

                        # Update UI indicators if they exist
                        if hasattr(self.mainwindow, 'ui'):
                            self.mainwindow.ui.btnPressureDataInd.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                            self.mainwindow.ui.btnPressureDataLoad.setStyleSheet(u'background-color: rgb(29, 78, 216);')
                            self.mainwindow.ui.btnPlots.setEnabled(True)
                except Exception as e:
                    print(f"Error loading pressure data: {str(e)}")

            # Load filtered temperature data if available
            if 'filtered_temp_data' in data:
                try:
                    print("Loading filtered temperature data")
                    filtered_data = data['filtered_temp_data']

                    # Create DataFrame with time data
                    if 'time' in filtered_data:
                        df_data = {'time': filtered_data['time']}
                    else:
                        # If no time data, create a sequence
                        if 'temperatures' in filtered_data and filtered_data['temperatures']:
                            first_temp_col = next(iter(filtered_data['temperatures'].values()))
                            df_data = {'time': list(range(len(first_temp_col)))}
                        else:
                            df_data = {'time': [0]}  # Fallback if no temperature data

                    # Add temperature data
                    if 'temperatures' in filtered_data:
                        df_data.update(filtered_data['temperatures'])

                    if hasattr(self.mainwindow, 'filtered_temp_data'):
                        self.mainwindow.filtered_temp_data = pd.DataFrame(df_data)
                        # Setting indicator color green
                        self.mainwindow.ui.tempMatrixIndicator.setStyleSheet("""
                                                                    background-color: rgb(6, 196, 142);
                                                                    border-radius: 7px;
                                                                    """
                                                                  )
                        print("Filtered temperature data loaded successfully")
                except Exception as e:
                    print(f"Error loading filtered temperature data: {str(e)}")

            # Load plot information if available using the new utility function
            if 'plots' in data:
                try:
                    # Import the plot_utils module
                    from data_recovery.plot_utils import load_plots_from_autosave

                    # Use the utility function to load plots
                    success = load_plots_from_autosave(self.mainwindow, data)
                    if success:
                        print("Plots loaded successfully")
                        # Enable the plots button
                        if hasattr(self.mainwindow, 'ui') and hasattr(self.mainwindow.ui, 'btnPlots'):
                            self.mainwindow.ui.btnPlots.setEnabled(True)
                    else:
                        print("No plots were loaded")

                except Exception as e:
                    print(f"Error loading plots: {str(e)}")
                    import traceback
                    traceback.print_exc()

            # Load photo information if available
            if 'photos' in data and hasattr(self.mainwindow, 'photo_widgets'):
                print("Loading photo information")

                for photo_type, photo_info in data['photos'].items():
                    if photo_type in self.mainwindow.photo_widgets:
                        widget_info = self.mainwindow.photo_widgets[photo_type]

                        if 'path' in photo_info and os.path.exists(photo_info['path']):
                            # Set the path in the line edit
                            widget_info['line_edit'].setText(photo_info['path'])

                            # Update the preview if possible
                            try:
                                if hasattr(self.mainwindow, 'update_photo_widget'):
                                    # Use the update_photo_widget method if available
                                    self.mainwindow.update_photo_widget(photo_type, photo_info['path'])
                                    print(f"Loaded photo: {photo_type}")
                                elif hasattr(self.mainwindow, 'photo_widgets') and photo_type in self.mainwindow.photo_widgets:
                                    # Manually update the preview
                                    preview_label = self.mainwindow.photo_widgets[photo_type]['preview']

                                    # Create preview pixmap
                                    preview_pixmap = QPixmap(photo_info['path'])
                                    if not preview_pixmap.isNull():
                                        scaled_pixmap = preview_pixmap.scaled(
                                            40, 40,
                                            Qt.AspectRatioMode.KeepAspectRatio,
                                            Qt.TransformationMode.SmoothTransformation
                                        )

                                        # Update preview label
                                        preview_label.setPixmap(scaled_pixmap)
                                        preview_label.setCursor(Qt.PointingHandCursor)
                                        preview_label.setProperty("image_path", photo_info['path'])

                                        # Disconnect any existing click handlers
                                        try:
                                            preview_label.disconnect()
                                        except:
                                            pass

                                        # Connect new click handler if show_full_photo method exists
                                        if hasattr(self.mainwindow, 'show_full_photo'):
                                            preview_label.mousePressEvent = lambda e, pid=photo_type: \
                                                self.mainwindow.show_full_photo(photo_info['path'], pid)

                                        print(f"Loaded photo: {photo_type}")
                            except Exception as e:
                                print(f"Error updating photo preview: {str(e)}")

            # Call the check_and_enable_plots_button method to ensure the plots button is enabled if valid data is loaded
            self.check_and_enable_plots_button()

            # Also directly force enable the plots button in the mainwindow
            if hasattr(self.mainwindow, 'force_enable_plots_button'):
                self.mainwindow.force_enable_plots_button()

            # Direct approach to enable the button
            if hasattr(self.mainwindow, 'ui') and hasattr(self.mainwindow.ui, 'btnPlots'):
                self.mainwindow.ui.btnPlots.setEnabled(True)

        except Exception as e:
            print(f"Error loading additional data: {str(e)}")
            import traceback
            traceback.print_exc()

    def auto_save(self):
        """Automatically save all data to temp_data.json"""
        try:
            print("Auto-save triggered - collecting data...")
            # Simply trigger data collection, which will handle the rest
            self.collect_requested.emit()
        except Exception as e:
            print(f"Error during auto-save: {str(e)}")
            import traceback
            traceback.print_exc()

    def force_save_all_data(self):
        """Force an immediate save of all data (for debugging)"""
        try:
            print("\n==== FORCE SAVING ALL DATA ====\n")

            # Collect all form data
            form_data = self.get_all_test_data()
            print(f"Collected form data with {len(form_data)} groups")

            # Create a data dictionary
            data = {}

            # Add form data
            for group_name, group_data in form_data.items():
                if group_name not in data:
                    data[group_name] = {}
                data[group_name].update(group_data)

            # Add additional data directly
            print("Adding temperature data...")
            if hasattr(self.mainwindow, 'temperature_data') and self.mainwindow.temperature_data is not None:
                temp_df = self.mainwindow.temperature_data
                data['temperature_data'] = {
                    'time': temp_df.index.tolist() if 'time' not in temp_df.columns else temp_df['time'].tolist(),
                    'temperatures': {col: temp_df[col].tolist() for col in temp_df.columns if col != 'time'}
                }
                print(f"Added temperature data with {len(data['temperature_data']['temperatures'])} columns")

            print("Adding pressure data...")
            if hasattr(self.mainwindow, 'pressure_data') and self.mainwindow.pressure_data is not None:
                pressure_df = self.mainwindow.pressure_data
                data['pressure_data'] = {
                    'time': pressure_df.index.tolist() if 'time' not in pressure_df.columns else pressure_df['time'].tolist(),
                    'pressures': {col: pressure_df[col].tolist() for col in pressure_df.columns if col != 'time'}
                }
                print(f"Added pressure data with {len(data['pressure_data']['pressures'])} columns")

            print("Adding photo data...")
            if hasattr(self.mainwindow, 'photo_widgets'):
                photos = {}
                for photo_type, widget_info in self.mainwindow.photo_widgets.items():
                    if 'line_edit' in widget_info and hasattr(widget_info['line_edit'], 'text'):
                        photo_path = widget_info['line_edit'].text()
                        if photo_path and os.path.exists(photo_path) and photo_path != 'No Photo Selected':
                            photos[photo_type] = {
                                'path': photo_path,
                                'label': widget_info.get('label', f'Photo {photo_type}')
                            }
                if photos:
                    data['photos'] = photos
                    print(f"Added {len(photos)} photos")

            # Save the data directly
            print(f"Saving data to {self.json_path}...")
            try:
                with open(self.json_path, 'w') as f:
                    json.dump(data, f, indent=4)

                print(f"Data saved successfully to {self.json_path}")
                print(f"File size: {os.path.getsize(self.json_path)} bytes")
            except PermissionError:
                print(f"Permission error when saving to {self.json_path}")

                # Create a new file with a unique name
                import uuid
                unique_path = f"{os.path.dirname(self.json_path)}\\temp_data_{uuid.uuid4().hex}.json"
                print(f"Creating file with unique name: {unique_path}")

                # Write data to the unique file
                with open(unique_path, 'w') as f:
                    json.dump(data, f, indent=4)

                print(f"Data saved to alternative file: {unique_path}")
                print(f"File size: {os.path.getsize(unique_path)} bytes")

            # Verify the save
            try:
                # Try to verify the original file
                with open(self.json_path, 'r') as f:
                    saved_data = json.load(f)
                    print(f"Verified file contains {len(saved_data)} top-level keys: {list(saved_data.keys())}")
            except (FileNotFoundError, PermissionError):
                # If we can't access the original file, try the unique file if it exists
                if 'unique_path' in locals():
                    try:
                        with open(unique_path, 'r') as f:
                            saved_data = json.load(f)
                            print(f"Verified alternative file contains {len(saved_data)} top-level keys: {list(saved_data.keys())}")
                    except Exception as e:
                        print(f"Error verifying alternative file: {str(e)}")
                else:
                    print("Could not verify any saved file")

            return True
        except Exception as e:
            print(f"Error force saving data: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def clear_temp_data(self):
        """Clear all temporary data"""
        try:
            # Create an empty data dictionary
            empty_data = {}

            # Prepare the save worker with the empty data
            if hasattr(self, 'save_worker') and self.save_worker is not None:
                self.save_worker.prepare_save(self.json_path, empty_data)

                # Trigger the save operation
                QMetaObject.invokeMethod(self.save_worker, "save", Qt.QueuedConnection)
                print("Clearing temporary data...")
            else:
                # Fallback if worker is not available
                try:
                    with open(self.json_path, 'w') as f:
                        json.dump({}, f)
                    print("Cleared temporary data (fallback method)")
                except PermissionError:
                    print(f"Permission error when clearing {self.json_path}")
                    # Try to create a new empty file with a unique name
                    import uuid
                    unique_path = os.path.join(os.path.dirname(self.json_path), f"temp_data_{uuid.uuid4().hex}.json")
                    with open(unique_path, 'w') as f:
                        json.dump({}, f)
                    print(f"Created new empty file: {unique_path}")
        except Exception as e:
            print(f"Error clearing temp data: {str(e)}")

    def save_all_test_data(self, json_file_path:str):
        """Save all the user input data in a JSON file"""
        for group_name, parameters in self.user_input_parameters.items():
            for field_name, (widget, value_type) in parameters.items():
                self.save_field(field_name, self.get_widget_value(widget, value_type), json_file_path)

    def get_all_test_data(self) -> dict:
        """Get all the current test data from widgets"""
        data = {}
        for group_name, parameters in self.user_input_parameters.items():
            group_data = {}
            for field_name, (widget, value_type) in parameters.items():
                value = self.get_widget_value(widget, value_type)
                if value is not None:
                    group_data[field_name] = value
            if group_data:
                data[group_name] = group_data

        return data

    @Slot()
    def trigger_auto_save(self):
        """Trigger an auto-save operation"""
        try:
            print("Auto-save triggered")
            # Request data collection
            self.collect_requested.emit()
        except Exception as e:
            print(f"Error triggering auto-save: {str(e)}")

    @Slot(dict)
    def on_data_collected(self, data):
        """Handle collected data"""
        try:
            print("\n==== DATA COLLECTION COMPLETED ====\n")
            print(f"Collected data has {len(data)} top-level keys")
            print(f"Top-level keys: {list(data.keys())}")

            # Check for non-serializable objects
            self.check_serializable(data)

            # Check for specific data types
            if 'temperature_data' in data:
                print(f"Temperature data found with {len(data['temperature_data']['temperatures'])} columns")
            else:
                print("No temperature data found")

            if 'pressure_data' in data:
                print(f"Pressure data found with {len(data['pressure_data']['pressures'])} columns")
            else:
                print("No pressure data found")

            if 'photos' in data:
                print(f"Photo data found with {len(data['photos'])} photos")
            else:
                print("No photo data found")

            if 'plots' in data:
                default_plots = len(data['plots'].get('default', []))
                custom_plots = len(data['plots'].get('custom', []))
                print(f"Plot data found with {default_plots} default plots and {custom_plots} custom plots")
            else:
                print("No plot data found")

            # Check if there's any real data to save
            has_real_data = False

            # Check for form data
            for group_name, group_data in data.items():
                if group_name not in ['temperature_data', 'pressure_data', 'filtered_temp_data'] and group_data:
                    has_real_data = True
                    break

            # Check for temperature data
            if 'temperature_data' in data and data['temperature_data'].get('temperatures') and \
               len(next(iter(data['temperature_data']['temperatures'].values()), [])) > 1:
                has_real_data = True

            # Check for pressure data
            if 'pressure_data' in data and data['pressure_data'].get('pressures') and \
               len(next(iter(data['pressure_data']['pressures'].values()), [])) > 1:
                has_real_data = True

            # Skip saving if there's no real data
            if not has_real_data and not data.get('photos'):
                print("No real data to save, skipping save operation")
                return

            # Lock the mutex to ensure thread safety
            self.mutex.lock()

            try:
                # Check if a save is already in progress
                if self.save_in_progress:
                    print("Save already in progress, skipping this save")
                    return

                # Mark that a save is in progress
                self.save_in_progress = True
            finally:
                # Always unlock the mutex
                self.mutex.unlock()

            # Prepare the save worker with the data
            self.save_worker.prepare_save(self.json_path, data)
            print(f"Prepared save worker with data for {self.json_path}")

            # Trigger the save operation
            QMetaObject.invokeMethod(self.save_worker, "save", Qt.QueuedConnection)
            print("Triggered save operation")

        except Exception as e:
            print(f"Error handling collected data: {str(e)}")
            import traceback
            traceback.print_exc()
            self.save_in_progress = False

    @Slot()
    def on_save_finished(self):
        """Called when a save operation is finished"""
        try:
            # Lock the mutex to ensure thread safety
            self.mutex.lock()

            try:
                # Mark that the save is no longer in progress
                self.save_in_progress = False
                print("Save operation completed")
            finally:
                # Always unlock the mutex
                self.mutex.unlock()

        except Exception as e:
            print(f"Error in on_save_finished: {str(e)}")
            self.save_in_progress = False

    # @Slot(str)
    # def log_progress(self, message):
    #     """Log progress messages from the worker thread"""
    #     print(message)

    def check_serializable(self, data, path=''):
        """Check if all objects in the data dictionary are JSON serializable"""
        if isinstance(data, dict):
            for key, value in data.items():
                current_path = f"{path}.{key}" if path else key
                if isinstance(value, (dict, list)):
                    self.check_serializable(value, current_path)
                elif not isinstance(value, (str, int, float, bool, type(None))):
                    # print(f"WARNING: Non-serializable object at {current_path}: {type(value).__name__}")
                    pass
        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = f"{path}[{i}]"
                if isinstance(item, (dict, list)):
                    self.check_serializable(item, current_path)
                elif not isinstance(item, (str, int, float, bool, type(None))):
                    # print(f"WARNING: Non-serializable object at {current_path}: {type(item).__name__}")
                    pass

    def stop_auto_save(self):
        """Stop the auto-save timer and any running worker threads"""
        try:
            # print("Stopping auto-save system...")

            # Stop the timer
            if hasattr(self, 'auto_save_timer') and self.auto_save_timer is not None:
                if hasattr(self.auto_save_timer, 'isActive') and self.auto_save_timer.isActive():
                    # print("Stopping auto-save timer")
                    self.auto_save_timer.stop()
                    # Set to None to prevent further access
                    self.auto_save_timer = None

            # Stop the collector thread
            if hasattr(self, 'collector_thread') and self.collector_thread is not None:
                if self.collector_thread.isRunning():
                    # print("Stopping data collector thread...")
                    self.collector_thread.quit()
                    self.collector_thread.wait(1000)  # Wait up to 1 second

                    # If still running, terminate it
                    if self.collector_thread.isRunning():
                        # print("Terminating data collector thread")
                        self.collector_thread.terminate()

                    # Set to None to prevent further access
                    self.collector = None
                    self.collector_thread = None

            # Stop the save thread
            if hasattr(self, 'save_thread') and self.save_thread is not None:
                if self.save_thread.isRunning():
                    # print("Stopping save thread...")
                    self.save_thread.quit()
                    self.save_thread.wait(1000)  # Wait up to 1 second

                    # If still running, terminate it
                    if self.save_thread.isRunning():
                        # print("Terminating save thread")
                        self.save_thread.terminate()

                    # Set to None to prevent further access
                    self.save_worker = None
                    self.save_thread = None

            # print("Auto-save system stopped successfully")

        except (RuntimeError, AttributeError, Exception) as e:
            # print(f"Error stopping auto-save: {str(e)}")
            import traceback
            traceback.print_exc()

    def __del__(self):
        """Destructor to ensure timer and threads are stopped"""
        try:
            self.stop_auto_save()
        except Exception:
            # Silently ignore errors in destructor
            pass
